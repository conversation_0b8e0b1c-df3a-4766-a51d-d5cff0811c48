'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON>olderO<PERSON>,
  FileText,
  ChevronRight,
  ChevronDown,
  Folder,
  File,
  Plus,
  Search,
  Sparkles,
  Code,
  BookOpen,
  Settings,
  RefreshCw,
  Edit
} from 'lucide-react';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { useAuth } from '../../app/context/AuthContext';
import { toast } from '../ui/use-toast';
import { createPMORecordFromForm } from '../../lib/firebase/pmoCollection';

interface FileSystemItem {
  name: string;
  path: string;
  type: 'file' | 'directory';
  children?: FileSystemItem[];
  expanded?: boolean;
  selected?: boolean;
}

interface SystemsDocumentationTabProps {}

export default function SystemsDocumentationTab({}: SystemsDocumentationTabProps) {
  const { user } = useAuth();
  const [selectedPaths, setSelectedPaths] = useState<string[]>([]);
  const [requestDescription, setRequestDescription] = useState('');
  const [customContext, setCustomContext] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [fileSystemTree, setFileSystemTree] = useState<FileSystemItem[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPath, setCurrentPath] = useState('c:\\backup');
  const [customPath, setCustomPath] = useState('');
  const [showCustomPathInput, setShowCustomPathInput] = useState(false);
  const [isLoadingFileSystem, setIsLoadingFileSystem] = useState(false);

  // Load file system from API
  const loadFileSystem = async (path: string) => {
    setIsLoadingFileSystem(true);
    try {
      const response = await fetch(`/api/codebase-documentation/file-system?rootPath=${encodeURIComponent(path)}&maxDepth=3&includeHidden=false`);
      if (!response.ok) {
        throw new Error('Failed to load file system');
      }
      const data = await response.json();
      if (data.success) {
        setFileSystemTree(data.fileSystem || []);
        setCurrentPath(data.rootPath);
      } else {
        throw new Error(data.error || 'Failed to load file system');
      }
    } catch (error: any) {
      console.error('Error loading file system:', error);
      toast({
        title: "File System Error",
        description: error.message || "Failed to load file system",
        variant: "destructive"
      });
      // Fallback to empty tree
      setFileSystemTree([]);
    } finally {
      setIsLoadingFileSystem(false);
    }
  };

  // Load initial file system
  useEffect(() => {
    loadFileSystem(currentPath);
  }, []);

  // Handle path change
  const handlePathChange = (newPath: string) => {
    setCurrentPath(newPath);
    loadFileSystem(newPath);
  };

  // Handle custom path submission
  const handleCustomPathSubmit = () => {
    if (customPath.trim()) {
      handlePathChange(customPath.trim());
      setShowCustomPathInput(false);
      setCustomPath('');
    }
  };

  const toggleExpanded = (path: string) => {
    const updateTree = (items: FileSystemItem[]): FileSystemItem[] => {
      return items.map(item => {
        if (item.path === path) {
          return { ...item, expanded: !item.expanded };
        }
        if (item.children) {
          return { ...item, children: updateTree(item.children) };
        }
        return item;
      });
    };
    setFileSystemTree(updateTree(fileSystemTree));
  };

  const toggleSelected = (path: string) => {
    setSelectedPaths(prev => 
      prev.includes(path) 
        ? prev.filter(p => p !== path)
        : [...prev, path]
    );
  };

  const renderFileSystemItem = (item: FileSystemItem, level: number = 0) => {
    const isSelected = selectedPaths.includes(item.path);
    const hasChildren = item.children && item.children.length > 0;
    
    return (
      <div key={item.path} className="select-none">
        <div 
          className={`flex items-center py-1 px-2 hover:bg-gray-700/50 cursor-pointer rounded ${
            isSelected ? 'bg-purple-600/20 border-l-2 border-purple-500' : ''
          }`}
          style={{ paddingLeft: `${level * 20 + 8}px` }}
          onClick={() => toggleSelected(item.path)}
        >
          {hasChildren && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                toggleExpanded(item.path);
              }}
              className="mr-1 p-0.5 hover:bg-gray-600 rounded"
            >
              {item.expanded ? (
                <ChevronDown className="w-3 h-3 text-gray-400" />
              ) : (
                <ChevronRight className="w-3 h-3 text-gray-400" />
              )}
            </button>
          )}
          
          {!hasChildren && <div className="w-4 mr-1" />}
          
          {item.type === 'directory' ? (
            <Folder className="w-4 h-4 text-blue-400 mr-2" />
          ) : (
            <File className="w-4 h-4 text-gray-400 mr-2" />
          )}
          
          <span className={`text-sm ${isSelected ? 'text-purple-200' : 'text-gray-300'}`}>
            {item.name}
          </span>
        </div>
        
        {hasChildren && item.expanded && item.children && (
          <div>
            {item.children.map(child => renderFileSystemItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  const handleSubmit = async () => {
    if (!user?.email) {
      toast({
        title: "Authentication Required",
        description: "Please log in to submit documentation requests.",
        variant: "destructive"
      });
      return;
    }

    if (selectedPaths.length === 0) {
      toast({
        title: "No Paths Selected",
        description: "Please select at least one file or directory to document.",
        variant: "destructive"
      });
      return;
    }

    if (!requestDescription.trim()) {
      toast({
        title: "Description Required",
        description: "Please provide a description of your documentation requirements.",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Create PMO record for codebase documentation
      const pmoFormInput = {
        title: `Codebase Documentation: ${selectedPaths.slice(0, 3).join(', ')}${selectedPaths.length > 3 ? '...' : ''}`,
        description: requestDescription,
        priority: 'Medium' as const,
        category: 'Documentation',
        sourceFile: `Selected paths: ${selectedPaths.join(', ')}`,
        fileName: `Codebase Documentation - ${new Date().toISOString().split('T')[0]}`,
        customContext: customContext.trim() || undefined,
        selectedFileId: undefined,
        selectedCategory: undefined,
        pmoAssessment: `Codebase documentation request for paths: ${selectedPaths.join(', ')}`
      };

      const pmoRecordId = await createPMORecordFromForm(user.email, pmoFormInput);

      toast({
        title: "Documentation Request Submitted",
        description: `Your codebase documentation request has been submitted successfully. PMO Record ID: ${pmoRecordId}`,
      });

      // Reset form
      setSelectedPaths([]);
      setRequestDescription('');
      setCustomContext('');

    } catch (error: any) {
      console.error('Error submitting documentation request:', error);
      toast({
        title: "Submission Failed",
        description: error.message || "Failed to submit documentation request.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-orange-600/20 rounded-lg">
            <BookOpen className="w-6 h-6 text-orange-400" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-white">Systems Documentation</h2>
            <p className="text-gray-400 text-sm">Generate comprehensive documentation for your codebase</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* File System Browser */}
        <div className="bg-gray-800 rounded-lg border border-gray-700">
          <div className="p-4 border-b border-gray-700">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-lg font-medium text-white flex items-center">
                <FolderOpen className="w-5 h-5 mr-2 text-orange-400" />
                Codebase Explorer
              </h3>
              <Button
                variant="outline"
                size="sm"
                className="text-xs"
                onClick={() => loadFileSystem(currentPath)}
                disabled={isLoadingFileSystem}
              >
                <RefreshCw className={`w-3 h-3 mr-1 ${isLoadingFileSystem ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>

            {/* Path Selection Controls */}
            <div className="mb-4 space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-300">Current Path:</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowCustomPathInput(!showCustomPathInput)}
                  className="text-orange-400 hover:text-orange-300 hover:bg-gray-700 text-xs h-auto py-1"
                >
                  <Edit className="h-3 w-3 mr-1" />
                  Change
                </Button>
              </div>

              <div className="flex items-center gap-2 text-sm">
                <span>📁</span>
                <span className="font-mono text-gray-300 bg-gray-900 px-2 py-1 rounded border border-gray-600 flex-1 text-xs">
                  {currentPath}
                </span>
              </div>

              {/* Quick Drive Selection */}
              <div className="flex flex-wrap gap-1">
                <span className="text-xs text-gray-400 mr-2">Quick:</span>
                {['C:\\', 'D:\\', 'E:\\'].map((drive) => (
                  <Button
                    key={drive}
                    variant="outline"
                    size="sm"
                    onClick={() => handlePathChange(drive)}
                    className="text-xs border-gray-600 text-gray-300 hover:bg-gray-700 px-2 py-1 h-auto"
                    disabled={isLoadingFileSystem}
                  >
                    {drive}
                  </Button>
                ))}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePathChange('c:\\backup')}
                  className="text-xs border-gray-600 text-gray-300 hover:bg-gray-700 px-2 py-1 h-auto"
                  disabled={isLoadingFileSystem}
                >
                  Backup
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePathChange('c:\\backup\\ike-project 9.4')}
                  className="text-xs border-gray-600 text-gray-300 hover:bg-gray-700 px-2 py-1 h-auto"
                  disabled={isLoadingFileSystem}
                >
                  Project
                </Button>
              </div>

              {/* Custom Path Input */}
              {showCustomPathInput && (
                <div className="flex gap-2">
                  <input
                    type="text"
                    value={customPath}
                    onChange={(e) => setCustomPath(e.target.value)}
                    placeholder="Enter custom path (e.g., C:\Users\<USER>\Projects)"
                    className="flex-1 bg-gray-900 border border-gray-600 rounded px-3 py-2 text-sm text-gray-300 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        handleCustomPathSubmit();
                      }
                    }}
                  />
                  <Button
                    onClick={handleCustomPathSubmit}
                    disabled={!customPath.trim() || isLoadingFileSystem}
                    className="bg-orange-600 hover:bg-orange-700 text-white px-3 py-2 text-xs"
                  >
                    Go
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setShowCustomPathInput(false);
                      setCustomPath('');
                    }}
                    className="border-gray-600 text-gray-300 hover:bg-gray-700 px-3 py-2 text-xs"
                  >
                    Cancel
                  </Button>
                </div>
              )}

              {/* Loading Indicator */}
              {isLoadingFileSystem && (
                <div className="flex items-center gap-2 text-sm text-orange-400">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-orange-400"></div>
                  <span>Loading file system...</span>
                </div>
              )}
            </div>

            <Input
              placeholder="Search files and directories..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="bg-gray-700 border-gray-600 text-white placeholder-gray-400"
            />
          </div>
          
          <div className="p-4 max-h-96 overflow-y-auto">
            {fileSystemTree.map(item => renderFileSystemItem(item))}
          </div>
          
          {selectedPaths.length > 0 && (
            <div className="p-4 border-t border-gray-700">
              <p className="text-sm text-gray-400 mb-2">Selected paths ({selectedPaths.length}):</p>
              <div className="space-y-1 max-h-20 overflow-y-auto">
                {selectedPaths.map(path => (
                  <div key={path} className="text-xs text-purple-300 bg-purple-900/20 px-2 py-1 rounded">
                    {path}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Documentation Request Form */}
        <div className="bg-gray-800 rounded-lg border border-gray-700">
          <div className="p-4 border-b border-gray-700">
            <h3 className="text-lg font-medium text-white flex items-center">
              <Code className="w-5 h-5 mr-2 text-orange-400" />
              Documentation Request
            </h3>
          </div>
          
          <div className="p-4 space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Documentation Requirements <span className="text-red-400">*</span>
              </label>
              <Textarea
                placeholder="Describe what kind of documentation you need for the selected codebase paths..."
                value={requestDescription}
                onChange={(e) => setRequestDescription(e.target.value)}
                className="bg-gray-700 border-gray-600 text-white placeholder-gray-400 min-h-[120px]"
                rows={5}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Additional Context (Optional)
              </label>
              <Textarea
                placeholder="Provide any additional context, specific requirements, or focus areas..."
                value={customContext}
                onChange={(e) => setCustomContext(e.target.value)}
                className="bg-gray-700 border-gray-600 text-white placeholder-gray-400"
                rows={3}
              />
            </div>
            
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting || selectedPaths.length === 0 || !requestDescription.trim()}
              className="w-full bg-orange-600 hover:bg-orange-700 text-white"
            >
              {isSubmitting ? (
                <>
                  <Settings className="w-4 h-4 mr-2 animate-spin" />
                  Submitting Request...
                </>
              ) : (
                <>
                  <Sparkles className="w-4 h-4 mr-2" />
                  Generate Documentation
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
