import { NextResponse } from 'next/server';
import { PMOAssessmentAgent } from '../../../../lib/agents/pmo/PMOAssessmentAgent';
import { queryDocumentsAgent } from '../../../../components/Agents/QueryDocumentsAgent';
import { PMOFormInput, AgenticTeamId, ModelProvider } from '../../../../lib/agents/pmo/PMOInterfaces';
import { createLlmService } from '../../../../lib/tools/llmServiceAdapter';
import { processWithGroq } from '../../../../lib/tools/groq-ai';
import { DEFAULT_COMPARISON_MODELS } from '../../../../lib/agents/investigative/constants';
//Add table chart tool from llm

/**
 * Intelligent team selection using Groq LLM
 */
async function determineTeamSelection(title: string, description: string): Promise<{
  selectedTeams: AgenticTeamId[];
  rationale: string;
  isInvestigativeResearch: boolean;
}> {
  const teamSelectionPrompt = `
You are an expert PMO (Project Management Office) analyst tasked with determining the most appropriate team(s) for a project request.

Available Teams:
1. **Marketing Team** - Marketing strategy, content creation, brand management, campaigns, market analysis and market research
2. **Research Team** - Data collection, analysis, research reports, academic research
3. **Software Design Team** - Software development, UI/UX design, coding, technical implementation, apps
4. **Sales Team** - Sales strategies, client relationships, revenue generation, business development
5. **Business Analysis Team** - Business process analysis, requirements gathering, strategic planning, operations
6. **Investigative Research Team** - In-depth editorial writing, comprehensive internet-sourced research, multi-source verification, journalistic-style reporting,
opinion pieces, analysis articles, investigative journalism, thought leadership content
7. **Codebase Documentation Team** - Comprehensive codebase documentation generation, architecture analysis, API documentation, technical documentation, code analysis

Project Details:
Title: "${title}"
Description: "${description}"

Instructions:
1. Analyze the project requirements carefully
2. Select the MOST APPROPRIATE team(s) based on the core activities required
3. Provide a clear rationale for your selection
4. Determine if this is investigative research (requires exposing, uncovering, investigating irregularities, corruption, fraud, or comprehensive fact-checking)

Respond in this exact JSON format:
{
  "selectedTeam": "TeamName",
  "rationale": "Clear explanation of why this team is best suited",
  "isInvestigativeResearch": true/false,
  "confidence": "High/Medium/Low"
}

Only select ONE primary team. Choose the team whose core competencies most closely match the project's primary objectives.
`;

  try {
    const response = await processWithGroq({
      prompt: teamSelectionPrompt,
      model: "deepseek-r1-distill-llama-70b"
    });

    // Parse the JSON response
    const cleanResponse = response.replace(/```json\n?|\n?```/g, '').trim();
    const analysis = JSON.parse(cleanResponse);

    // Map team name to AgenticTeamId
    const teamMapping: { [key: string]: AgenticTeamId } = {
      'Marketing Team': AgenticTeamId.Marketing,
      'Marketing': AgenticTeamId.Marketing,
      'Research Team': AgenticTeamId.Research,
      'Research': AgenticTeamId.Research,
      'Software Design Team': AgenticTeamId.SoftwareDesign,
      'Software Design': AgenticTeamId.SoftwareDesign,
      'Sales Team': AgenticTeamId.Sales,
      'Sales': AgenticTeamId.Sales,
      'Business Analysis Team': AgenticTeamId.BusinessAnalysis,
      'Business Analysis': AgenticTeamId.BusinessAnalysis,
      'Investigative Research Team': AgenticTeamId.InvestigativeResearch,
      'Investigative Research': AgenticTeamId.InvestigativeResearch,
      'Codebase Documentation Team': AgenticTeamId.CodebaseDocumentation,
      'Codebase Documentation': AgenticTeamId.CodebaseDocumentation
    };

    const selectedTeamId = teamMapping[analysis.selectedTeam] || AgenticTeamId.Research; // Default fallback

    return {
      selectedTeams: [selectedTeamId],
      rationale: analysis.rationale || "Team selected based on project requirements analysis.",
      isInvestigativeResearch: analysis.isInvestigativeResearch || false
    };

  } catch (error) {
    console.error('Error in LLM team selection:', error);

    // Fallback to simple keyword detection
    const descriptionLower = description.toLowerCase();
    const titleLower = title.toLowerCase();
    const combined = `${titleLower} ${descriptionLower}`;

    if (combined.includes('investigat') || combined.includes('expose') || combined.includes('corruption') ||
        combined.includes('fraud') || combined.includes('scandal') || combined.includes('uncover')) {
      return {
        selectedTeams: [AgenticTeamId.InvestigativeResearch],
        rationale: "Investigative Research Team selected based on keywords indicating investigative analysis requirements.",
        isInvestigativeResearch: true
      };
    } else if (combined.includes('market') || combined.includes('brand') || combined.includes('campaign')) {
      return {
        selectedTeams: [AgenticTeamId.Marketing],
        rationale: "Marketing Team selected based on marketing-related keywords.",
        isInvestigativeResearch: false
      };
    } else if (combined.includes('software') || combined.includes('develop') || combined.includes('code') || combined.includes('app')) {
      return {
        selectedTeams: [AgenticTeamId.SoftwareDesign],
        rationale: "Software Design Team selected based on development-related keywords.",
        isInvestigativeResearch: false
      };
    } else {
      return {
        selectedTeams: [AgenticTeamId.Research],
        rationale: "Research Team selected as default for analysis and research tasks.",
        isInvestigativeResearch: false
      };
    }
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const {
      userId,
      title,
      description,
      priority,
      category,
      contextOptions,
      modelProvider, // Extracted from body
      modelName,     // Extracted from body
    } = body;

    // Modified: Added modelProvider and modelName to the required fields check
    if (!userId || !title || !description || !modelProvider || !modelName) {
      return NextResponse.json(
        { error: 'Missing required fields (userId, title, description, modelProvider, modelName)' },
        { status: 400 }
      );
    }

    // Validate modelProvider to ensure it's a valid ModelProvider type
    const validModelProvider = (modelProvider && ['openai', 'anthropic', 'groq', 'google'].includes(modelProvider))
      ? modelProvider as ModelProvider
      : 'openai'; // Default to 'openai' if invalid or not provided

    // Create an LLM service adapter with the validated model provider
    const llmService = createLlmService(validModelProvider);

    const pmoAssessmentAgent = new PMOAssessmentAgent(
      {
        userId: userId,
        includeExplanation: true,
        streamResponse: false // Streaming from API route is more complex, keep false for now
      },
      llmService // Pass the LLM service as the second parameter
    );

    // Arm the agent with necessary tools
    pmoAssessmentAgent.setQueryDocumentsAgent(queryDocumentsAgent);

    const pmoInput: PMOFormInput = {
      title,
      description,
      priority: priority || 'Medium',
      category: category || 'PMO Request',
      contextOptions: {
        customContext: contextOptions?.customContext || undefined,
        fileIds: contextOptions?.fileIds || [],
        categoryIds: contextOptions?.categoryIds || [],
      },
      // These are part of the input to generateAssessment,
      // but the agent itself also needs configuration for its own LLM.
      // Validate modelProvider to ensure it's a valid ModelProvider type
      modelProvider: (modelProvider && ['openai', 'anthropic', 'groq', 'google'].includes(modelProvider))
        ? modelProvider as ModelProvider
        : 'openai', // Default to 'openai' if invalid or not provided
      modelName: modelName || 'o3-2025-04-16', // Provide a default model name if not specified
    };

    // Use LLM-based intelligent team selection
    console.log('Determining team selection using LLM...');
    const teamSelection = await determineTeamSelection(title, description);

    const selectedTeams = teamSelection.selectedTeams;
    const isInvestigativeResearch = teamSelection.isInvestigativeResearch;
    const teamSelectionRationale = teamSelection.rationale;

    console.log(`Team selection result: ${selectedTeams.map(t => Object.keys(AgenticTeamId).find(key => (AgenticTeamId as any)[key] === t) || 'Unknown').join(', ')}, Investigative: ${isInvestigativeResearch}`);

    // Generate a simple assessment for team selection
    const simpleAssessment = `Project Title: ${title}\nDescription: ${description}\nPriority: ${priority || 'Medium'}\nCategory: ${category || 'Unknown'}`;

    // Get context chunks
    let contextChunks: any[] = [];

    // Process context options to get context chunks
    if (contextOptions) {
      if (contextOptions.customContext) {
        // For custom context, create a synthetic chunk
        contextChunks = [{
          content: contextOptions.customContext,
          metadata: {
            fileName: 'Custom Context',
            source: 'User Input',
            relevance: 1.0
          },
          text: contextOptions.customContext
        }];
      } else if (contextOptions.fileIds && contextOptions.fileIds.length > 0) {
        // Query documents by file IDs
        const queryResult = await queryDocumentsAgent.queryDocuments({
          query: description,
          fileIds: contextOptions.fileIds,
          maxResults: 5,
          userId: userId
        });

        if (queryResult.success) {
          contextChunks = queryResult.chunks || [];

          // If no chunks were returned but we have results, create synthetic chunks
          if (contextChunks.length === 0 && queryResult.results.length > 0) {
            contextChunks = queryResult.results.map((result, index) => ({
              content: result,
              metadata: {
                fileName: `Result ${index + 1}`,
                source: contextOptions.fileIds[0] || 'Unknown',
                relevance: 1.0 - (index * 0.1) // Decreasing relevance for each result
              },
              text: result
            }));
          }
        }
      } else if (contextOptions.categoryIds && contextOptions.categoryIds.length > 0) {
        // Query documents by category IDs
        const queryResult = await queryDocumentsAgent.queryDocumentsByCategory({
          query: description,
          categoryIds: contextOptions.categoryIds,
          maxResults: 5,
          userId: userId
        });

        if (queryResult.success) {
          contextChunks = queryResult.chunks || [];

          // If no chunks were returned but we have results, create synthetic chunks
          if (contextChunks.length === 0 && queryResult.results.length > 0) {
            contextChunks = queryResult.results.map((result, index) => ({
              content: result,
              metadata: {
                fileName: `Result ${index + 1}`,
                source: contextOptions.categoryIds[0] || 'Unknown',
                relevance: 1.0 - (index * 0.1) // Decreasing relevance for each result
              },
              text: result
            }));
          }
        }
      }
    }

    // Ensure we always have at least one context chunk
    if (contextChunks.length === 0) {
      const defaultContent = description;
      contextChunks = [{
        content: defaultContent,
        metadata: {
          fileName: 'Project Description',
          source: 'User Input',
          relevance: 1.0
        },
        text: defaultContent
      }];
    }

    // Now generate the requirements specification directly
    const requirementsSpec = await pmoAssessmentAgent.generateRequirementsSpecificationDirectly(
      pmoInput,
      simpleAssessment,
      selectedTeams,
      contextChunks
    );

    // Team selection rationale is now provided by the LLM analysis above

    // Construct the text to be displayed in the UI
    let fullAssessmentText = `## Requirements Specification for: ${pmoInput.title}\n\n${requirementsSpec}`;

    // Add team selection information
    if (selectedTeams.length > 0) {
      const teamNames = selectedTeams.map(teamId => {
        // Get the team name from the enum value
        const getTeamName = (id: AgenticTeamId): string => {
          switch(id) {
            case AgenticTeamId.Marketing: return "Marketing";
            case AgenticTeamId.Research: return "Research";
            case AgenticTeamId.SoftwareDesign: return "Software Design";
            case AgenticTeamId.Sales: return "Sales";
            case AgenticTeamId.BusinessAnalysis: return "Business Analysis";
            case AgenticTeamId.InvestigativeResearch: return "Investigative Research";
            default:
              // Attempt to get string representation of enum key if it's a less common/new one
              const enumKey = Object.keys(AgenticTeamId).find(key => (AgenticTeamId as any)[key] === id);
              return enumKey || String(id);
          }
        };
        return getTeamName(teamId);
      }).join(', ');

      fullAssessmentText += `\n\n### Proposed Team Delegation\n**Teams:** ${teamNames}\n**Rationale:** ${teamSelectionRationale}`;
    }

    // Add investigative research specific recommendations
    if (isInvestigativeResearch) {
      fullAssessmentText += `\n\n### Investigative Research Configuration\n`;
      fullAssessmentText += `**Investigation Type:** Comprehensive investigative analysis\n`;
      fullAssessmentText += `**Recommended Approach:**\n`;
      fullAssessmentText += `- Multi-LLM comparison for enhanced accuracy\n`;
      fullAssessmentText += `- Specialized journalist AI agents for different perspectives\n`;
      fullAssessmentText += `- Cross-verification of findings across multiple sources\n`;
      fullAssessmentText += `- Professional consolidation of investigation results\n\n`;
      fullAssessmentText += `**Suggested Models:**\n`;
      fullAssessmentText += `- Comparison Models: ${DEFAULT_COMPARISON_MODELS.join(', ')}\n`;
      fullAssessmentText += `- Criteria Model: Claude Sonnet 4.0 (for analytical rigor)\n`;
      fullAssessmentText += `- Optimization Model: GPT-4o (for process efficiency)\n`;
      fullAssessmentText += `- Assessment Model: Claude Sonnet 4.0 (for quality evaluation)\n`;
      fullAssessmentText += `- Consolidation Model: Claude Sonnet 4.0 (for final reporting)\n\n`;
      fullAssessmentText += `**Expected Deliverables:**\n`;
      fullAssessmentText += `- Individual journalist investigation reports\n`;
      fullAssessmentText += `- Consolidated professional investigation report\n`;
      fullAssessmentText += `- Key findings and recommendations\n`;
      fullAssessmentText += `- Source verification and credibility assessment\n`;
      fullAssessmentText += `- PDF documentation for record keeping`;
    }

    return NextResponse.json({
      success: true,
      assessmentText: fullAssessmentText,
      selectedTeams: selectedTeams,
      requirementsSpecification: requirementsSpec,
      isInvestigativeResearch: isInvestigativeResearch
    });

  } catch (error: any) {
    console.error('API - Error generating PMO assessment:', error);
    return NextResponse.json({
      error: `Failed to generate assessment: ${error.message}`
    }, { status: 500 });
  }
}